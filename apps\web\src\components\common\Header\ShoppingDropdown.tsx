'use client'
import { useC<PERSON>back, useEffect, useRef, useState } from 'react'
import {
  ROUTE,
  TRACK_EVENT,
  useCurrentTime,
  useGetCategoryProductsQuery,
  useLazyGetCategoryProductsQuery,
  useVolcAnalytics,
} from '@ninebot/core'
import { HomeProduct } from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import clsx from 'clsx'

import { IconArrow, ProductCard } from '@/components'
import { calculateViewportHeight, useViewportHeight } from '@/hooks/useViewportHeight'
import { Category } from '@/types/category'

interface ShoppingDropdownProps {
  isOpen: boolean
  categories: Category
  categoryAllUid: string
  onClose?: () => void
  onMouseEnter?: () => void
  onMouseLeave?: () => void
}

interface CategorySection {
  uid: string
  name: string
  url_path: string
  url_suffix: string
  products: HomeProduct[]
  hasMore: boolean
}

export default function ShoppingDropdown({
  isOpen,
  categories,
  categoryAllUid,
  onClose,
  onMouseEnter,
  onMouseLeave,
}: ShoppingDropdownProps) {
  const { reportEvent } = useVolcAnalytics()
  const { openPage } = useNavigate()
  const { timestamp: currentTime } = useCurrentTime()
  const dropdownRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const leftNavRef = useRef<HTMLDivElement>(null)

  // 获取真实的视口高度，解决移动端兼容性问题
  const viewportHeight = useViewportHeight()

  // 触摸设备检测（用于样式优化）
  const isTouchDevice =
    typeof window !== 'undefined' && ('ontouchstart' in window || navigator.maxTouchPoints > 0)

  // 检测左侧导航是否需要滚动
  const [leftNavNeedsScroll, setLeftNavNeedsScroll] = useState(false)
  // 用户手动滚动检测
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  const userScrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  // 添加防抖切换的引用
  const switchTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  // 计算基于屏幕宽度的动态弹窗高度
  const calculateDropdownHeight = useCallback(() => {
    // 获取屏幕宽度
    const screenWidth = window.innerWidth

    // 计算基础偏移量（header高度 + margin-top）
    const baseOffset = 56 + 32 // 56px(header) + 32px(margin-top)

    // 根据屏幕宽度计算底部边距
    let bottomMargin: number
    // if (screenWidth <= 1024) {
    //   bottomMargin = 104 // 最小高度104px
    // } else if (screenWidth >= 1902) {
    //   bottomMargin = 130 // 最大高度130px
    // } else {
    //   // 在1024px和1902px之间进行线性插值
    //   const ratio = (screenWidth - 1024) / (1902 - 1024)
    //   bottomMargin = 104 + ratio * (130 - 104)
    // }
    if (screenWidth >= 1440) {
      bottomMargin = 130 // 最大高度130px
    } else {
      bottomMargin = 104 // 最小高度104px
    }

    // 总偏移量 = 基础偏移量 + 底部边距
    const totalOffset = baseOffset + bottomMargin

    return calculateViewportHeight(totalOffset, viewportHeight)
  }, [viewportHeight])

  // 状态管理
  const [selectedCategoryUid, setSelectedCategoryUid] = useState<string>('')
  const [categorySections, setCategorySections] = useState<CategorySection[]>([])
  const [isScrolling, setIsScrolling] = useState(false) // 标记是否正在程序化滚动
  const [isAutoScrolling, setIsAutoScrolling] = useState(false) // 标记是否正在自动滚动左侧导航

  // 获取分类产品数据 - 获取更多产品以便为各个L2分类分配
  const { data: categoryProductsData, isLoading: isProductsLoading } = useGetCategoryProductsQuery(
    {
      filters: { category_uid: { eq: categoryAllUid } },
      pageSize: 50, // 增加获取的产品数量，确保有足够的产品为各个L2分类分配
      currentPage: 1,
      customAttributesV3Filter: {
        filter_attributes: [
          'product_tag',
          'paymeng_method',
          'max_usage_limit_ncoins',
          'is_insurance',
          'insurance_link',
        ],
      },
    },
    { skip: !categoryAllUid || !isOpen },
  )

  // 为每个L2分类单独获取产品数据

  const [l2ProductsLoading, setL2ProductsLoading] = useState<Record<string, boolean>>({})

  // 使用lazy query来为每个L2分类获取产品
  const [fetchCategoryProducts] = useLazyGetCategoryProductsQuery()

  // 检测左侧导航容器是否可滚动，并添加用户手动滚动监听
  useEffect(() => {
    const checkScrollable = () => {
      if (leftNavRef.current) {
        const { scrollHeight, clientHeight } = leftNavRef.current
        setLeftNavNeedsScroll(scrollHeight > clientHeight)
      }
    }

    // 用户手动滚动处理
    const handleUserScroll = () => {
      // 标记用户正在手动滚动
      setIsUserScrolling(true)

      // 清除之前的定时器
      if (userScrollTimeoutRef.current) {
        clearTimeout(userScrollTimeoutRef.current)
      }

      // 设置定时器，在用户停止滚动2秒后恢复自动定位
      userScrollTimeoutRef.current = setTimeout(() => {
        setIsUserScrolling(false)
      }, 2000)
    }

    // 初始检测
    checkScrollable()

    // 监听窗口大小变化
    window.addEventListener('resize', checkScrollable)

    // 监听分类数据变化
    const timeoutId = setTimeout(checkScrollable, 100)

    // 添加用户滚动监听
    const leftNavElement = leftNavRef.current
    if (leftNavElement) {
      leftNavElement.addEventListener('scroll', handleUserScroll, { passive: true })
    }

    return () => {
      window.removeEventListener('resize', checkScrollable)
      clearTimeout(timeoutId)

      // 清理用户滚动监听
      if (leftNavElement) {
        leftNavElement.removeEventListener('scroll', handleUserScroll)
      }

      // 清理定时器
      if (userScrollTimeoutRef.current) {
        clearTimeout(userScrollTimeoutRef.current)
      }
    }
  }, [categorySections, isOpen])

  // 处理分类数据，设置默认选中分类
  useEffect(() => {
    if (!categories || !isOpen) return

    // 收集所有L3级分类
    const allL3Categories: Array<{ uid: string; name: string; parentName: string }> = []

    categories.forEach((l1Category) => {
      if (l1Category && l1Category.children) {
        l1Category.children.forEach((l2Category) => {
          if (l2Category && l2Category.children) {
            l2Category.children.forEach((l3Category) => {
              if (l3Category && l3Category.include_in_menu === 1) {
                allL3Categories.push({
                  uid: l3Category.uid,
                  name: l3Category.name || '',
                  parentName: l2Category.name || '',
                })
              }
            })
          }
        })
      }
    })

    // 设置默认选中的分类（优先选择包含"新品"或"滑板车"的分类）
    if (allL3Categories.length > 0 && !selectedCategoryUid) {
      const defaultCategory =
        allL3Categories.find((cat) => cat.name.includes('新品') || cat.name.includes('滑板车')) ||
        allL3Categories[0]

      if (defaultCategory) {
        setSelectedCategoryUid(defaultCategory.uid)
      }
    }
  }, [categories, isOpen, selectedCategoryUid])

  // 处理产品数据 - 为每个L3分类获取对应的产品
  useEffect(() => {
    if (!categoryProductsData?.categories?.items?.[0]) return

    const categoryData = categoryProductsData.categories.items[0]
    const children = categoryData.children || []

    // 获取所有L3级分类（包含在菜单中的）
    const validL3Categories: Array<{
      uid: string
      name: string
      include_in_menu: number
      url_path?: string
      url_suffix?: string
    }> = []
    children.forEach((l2Category) => {
      if (l2Category && l2Category.children) {
        l2Category.children.forEach((l3Category) => {
          if (l3Category && l3Category.include_in_menu === 1) {
            validL3Categories.push({
              uid: l3Category.uid,
              name: l3Category.name || '',
              include_in_menu: l3Category.include_in_menu || 1,
              url_path: l3Category.url_path || '',
              url_suffix: l3Category.url_suffix || '',
            })
          }
        })
      }
    })

    // 显示所有L3级别分类
    const limitedL3Categories = validL3Categories

    // 为每个L3分类获取对应的产品
    const fetchL3CategoryProducts = async () => {
      const newL3Products: Record<string, HomeProduct[]> = {}
      const newL3Loading: Record<string, boolean> = {}

      // 初始化加载状态
      limitedL3Categories.forEach((child) => {
        if (child) {
          newL3Loading[child.uid] = true
        }
      })
      setL2ProductsLoading(newL3Loading)

      // 为每个L3分类获取产品
      const productPromises = limitedL3Categories.map(async (child) => {
        if (!child) return null

        try {
          // 为每个L3分类单独查询产品数据
          const result = await fetchCategoryProducts({
            filters: { category_uid: { eq: child.uid } },
            pageSize: 4, // 每个L3分类获取4个产品
            currentPage: 1,
            customAttributesV3Filter: {
              filter_attributes: [
                'product_tag',
                'paymeng_method',
                'max_usage_limit_ncoins',
                'is_insurance',
                'insurance_link',
              ],
            },
          })

          const l3Products = result.data?.categories?.items?.[0]?.products?.items || []
          const categoryProducts = l3Products.slice(0, 4) as HomeProduct[]

          // 如果L3分类没有产品，则从L1级别的产品中分配
          if (categoryProducts.length === 0) {
            const allProducts = categoryData.products?.items || []
            const productsPerCategory = 4
            const fallbackProducts: HomeProduct[] = []

            // 使用改进的分配策略：基于分类UID的哈希值来分配产品
            const categoryIndex = limitedL3Categories.findIndex((c) => c?.uid === child.uid)
            const startIndex = (categoryIndex * productsPerCategory) % allProducts.length

            for (let i = 0; i < productsPerCategory && allProducts.length > 0; i++) {
              const productIndex = (startIndex + i) % allProducts.length
              const product = allProducts[productIndex]
              if (product && !fallbackProducts.find((p) => p.sku === product.sku)) {
                fallbackProducts.push(product as HomeProduct)
              }
            }

            newL3Products[child.uid] = fallbackProducts
            return {
              uid: child.uid,
              name: child.name || '',
              url_path: child.url_path || '',
              url_suffix: child.url_suffix || '',
              products: fallbackProducts,
              hasMore: allProducts.length > productsPerCategory,
            }
          }

          newL3Products[child.uid] = categoryProducts
          return {
            uid: child.uid,
            name: child.name || '',
            url_path: child.url_path || '',
            url_suffix: child.url_suffix || '',
            products: categoryProducts,
            hasMore: l3Products.length > 4,
          }
        } catch (error) {
          console.error(`Failed to fetch products for category ${child.uid}:`, error)
          newL3Products[child.uid] = []
          return {
            uid: child.uid,
            name: child.name || '',
            url_path: child.url_path || '',
            url_suffix: child.url_suffix || '',
            products: [],
            hasMore: false,
          }
        }
      })

      const results = await Promise.all(productPromises)
      const sectionsData = results.filter(Boolean) as CategorySection[]

      setCategorySections(sectionsData)

      // 清除加载状态
      const clearedLoading: Record<string, boolean> = {}
      limitedL3Categories.forEach((child) => {
        if (child) {
          clearedLoading[child.uid] = false
        }
      })
      setL2ProductsLoading(clearedLoading)
    }

    fetchL3CategoryProducts()
  }, [categoryProductsData, fetchCategoryProducts])

  // 左侧导航栏动态定位函数
  const scrollToSelectedCategory = useCallback(
    (categoryUid: string) => {
      if (!leftNavNeedsScroll || !leftNavRef.current || !categoryUid || isAutoScrolling) return

      const leftNavContainer = leftNavRef.current
      const selectedButton = leftNavContainer.querySelector(
        `button[data-category-uid="${categoryUid}"]`,
      ) as HTMLElement

      if (!selectedButton) return

      setIsAutoScrolling(true)

      // 计算选中项相对于容器的位置
      const containerRect = leftNavContainer.getBoundingClientRect()
      const buttonRect = selectedButton.getBoundingClientRect()

      // 计算选中项在容器中的相对位置
      const buttonTop = buttonRect.top - containerRect.top + leftNavContainer.scrollTop
      const buttonHeight = buttonRect.height
      const containerHeight = containerRect.height

      // 计算目标滚动位置，让选中项居中显示
      const targetScrollTop = buttonTop - containerHeight / 2 + buttonHeight / 2

      // 确保滚动位置在有效范围内
      const maxScrollTop = leftNavContainer.scrollHeight - containerHeight
      const finalScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop))

      // 使用平滑滚动
      leftNavContainer.scrollTo({
        top: finalScrollTop,
        behavior: 'smooth',
      })

      // 重置自动滚动标记
      setTimeout(() => {
        setIsAutoScrolling(false)
      }, 600) // 给滚动动画足够的时间
    },
    [leftNavNeedsScroll, isAutoScrolling],
  )

  // 处理左侧分类点击
  const handleCategoryClick = useCallback(
    (categoryUid: string, categoryName: string) => {
      setSelectedCategoryUid(categoryUid)
      setIsScrolling(true) // 标记开始程序化滚动

      // 埋点：点击分类
      reportEvent(TRACK_EVENT.shop_homepage_tab_click, {
        category_id: categoryUid,
        category_name: categoryName,
        click_position: 'left_navigation',
        previous_category: selectedCategoryUid,
      })

      // 滚动到对应的产品区域
      const targetElement = document.getElementById(`category-section-${categoryUid}`)
      if (targetElement && contentRef.current) {
        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' })

        // 滚动完成后重置标记
        setTimeout(() => {
          setIsScrolling(false)
        }, 800) // 给滚动动画足够的时间
      }
    },
    [reportEvent, selectedCategoryUid],
  )

  // 获取分类的显示名称（用于右侧区域标题）
  const getCategoryDisplayName = useCallback(
    (categoryUid: string) => {
      for (const l1Category of categories || []) {
        if (l1Category?.children) {
          for (const l2Category of l1Category.children) {
            if (l2Category?.uid === categoryUid) {
              return l2Category.name || ''
            }
          }
        }
      }
      return ''
    },
    [categories],
  )

  // 处理弹窗关闭
  const handleClose = useCallback(() => {
    // 埋点：弹窗关闭
    reportEvent(TRACK_EVENT.shop_classfication_exposure, {
      category_type: 'shopping_dropdown_close',
      category_name: '购车',
      close_time: Date.now(),
      selected_category: getCategoryDisplayName(selectedCategoryUid),
    })

    // 调用外部传入的onClose回调
    onClose?.()

    // 通过自定义事件通知父组件关闭弹窗（保留原有逻辑作为备用）
    const closeEvent = new CustomEvent('shopping-dropdown-close')
    window.dispatchEvent(closeEvent)
  }, [reportEvent, selectedCategoryUid, getCategoryDisplayName, onClose])

  // 处理"查看全部"点击
  const handleViewAllClick = useCallback(
    (section: CategorySection) => {
      // 埋点：点击查看全部
      reportEvent(TRACK_EVENT.shop_homepage_tab_click, {
        category_id: section.uid,
        category_name: section.name,
      })

      handleClose()
      openPage({
        route: ROUTE.catalogCategory,
        url: `/${section.url_path}${section.url_suffix}`,
        value: section.uid,
      })
    },
    [reportEvent, handleClose, openPage],
  )

  // 处理产品点击
  const handleProductClick = useCallback(
    (product?: HomeProduct) => {
      // 埋点：点击产品
      if (product) {
        reportEvent(TRACK_EVENT.shop_homepage_category_product_picture_click, {
          product_id: product.sku,
          product_name: product.name,
          category_id: selectedCategoryUid,
          category_name: getCategoryDisplayName(selectedCategoryUid),
          click_position: 'shopping_dropdown',
        })
      }

      handleClose()
    },
    [handleClose, reportEvent, selectedCategoryUid, getCategoryDisplayName],
  )

  // 处理点击外部关闭（保留点击关闭功能作为备用）
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      // 检查点击是否在弹窗内部
      if (dropdownRef.current && !dropdownRef.current.contains(target)) {
        // 检查点击是否在购车导航项上（避免立即重新打开）
        const shoppingNavItems = document.querySelectorAll('[data-nav-shopping]')
        for (const navItem of shoppingNavItems) {
          if (navItem.contains(target)) {
            return
          }
        }

        handleClose()
      }
    }

    if (isOpen) {
      // 延迟添加事件监听器，避免立即触发
      const timer = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside)
      }, 100)

      return () => {
        clearTimeout(timer)
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [isOpen, handleClose])

  // 处理键盘导航
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return

      if (event.key === 'Escape') {
        handleClose()
        return
      }

      // 获取所有L2级分类
      const allL2Categories: Array<{ uid: string; name: string }> = []
      categories?.forEach((l1Category) => {
        if (l1Category?.children) {
          l1Category.children.forEach((l2Category) => {
            if (l2Category && l2Category.include_in_menu === 1) {
              allL2Categories.push({
                uid: l2Category.uid,
                name: l2Category.name || '',
              })
            }
          })
        }
      })

      const currentIndex = allL2Categories.findIndex((cat) => cat.uid === selectedCategoryUid)

      if (event.key === 'ArrowDown') {
        event.preventDefault()
        const nextIndex = (currentIndex + 1) % allL2Categories.length
        const nextCategory = allL2Categories[nextIndex]
        if (nextCategory) {
          handleCategoryClick(nextCategory.uid, nextCategory.name)
        }
      } else if (event.key === 'ArrowUp') {
        event.preventDefault()
        const prevIndex = currentIndex <= 0 ? allL2Categories.length - 1 : currentIndex - 1
        const prevCategory = allL2Categories[prevIndex]
        if (prevCategory) {
          handleCategoryClick(prevCategory.uid, prevCategory.name)
        }
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, handleClose, categories, selectedCategoryUid, handleCategoryClick])

  // 实现滚动联动：监听右侧内容区滚动，自动更新左侧选中状态
  useEffect(() => {
    if (!isOpen || isScrolling || categorySections.length === 0) return

    const contentElement = contentRef.current
    if (!contentElement) return

    // 获取最后一个分类的ID
    const lastCategoryUid = categorySections[categorySections.length - 1]?.uid
    const secondLastCategoryUid = categorySections[categorySections.length - 2]?.uid

    // 用于防抖的变量
    let scrollTimeout: NodeJS.Timeout | null = null
    let isBottomHandled = false

    // 处理滚动到底部的情况（改进版）
    const handleScroll = () => {
      if (!contentElement || isBottomHandled) return false

      const { scrollTop, scrollHeight, clientHeight } = contentElement
      const scrolledToBottom = scrollHeight - scrollTop - clientHeight < 50 // 增加阈值

      if (scrollTimeout) {
        clearTimeout(scrollTimeout)
      }

      if (scrolledToBottom && lastCategoryUid) {
        if (selectedCategoryUid !== lastCategoryUid) {
          scrollTimeout = setTimeout(() => {
            isBottomHandled = true

            // 清除切换定时器，立即执行底部切换
            if (switchTimeoutRef.current) {
              clearTimeout(switchTimeoutRef.current)
            }

            reportEvent(TRACK_EVENT.shop_homepage_tab_click, {
              category_id: lastCategoryUid,
              category_name: getCategoryDisplayName(lastCategoryUid),
              click_position: 'scroll_auto',
              previous_category: selectedCategoryUid,
              trigger_type: 'scroll_bottom',
            })

            setSelectedCategoryUid(lastCategoryUid)

            setTimeout(() => {
              isBottomHandled = false
            }, 1000) // 增加底部处理的持续时间
          }, 200) // 增加底部检测的延迟

          return true
        }
      }

      return false
    }

    const observer = new IntersectionObserver(
      (entries) => {
        if (isBottomHandled || handleScroll()) return

        // 过滤出正在相交的条目
        const intersectingEntries = entries.filter((entry) => entry.isIntersecting)

        if (intersectingEntries.length === 0) return

        // 改进的选择逻辑：优先选择视口中心区域的元素
        const viewportCenter = contentElement.clientHeight / 2
        let bestEntry = intersectingEntries[0]
        let bestScore = -1

        intersectingEntries.forEach((entry) => {
          const rect = entry.boundingClientRect
          const elementCenter = rect.top + rect.height / 2
          const distanceFromCenter = Math.abs(elementCenter - viewportCenter)

          // 计算综合得分：相交比例 * 权重 - 距离中心的距离 * 权重
          const intersectionScore = entry.intersectionRatio * 100
          const centerScore = Math.max(0, 100 - distanceFromCenter)
          const totalScore = intersectionScore * 0.6 + centerScore * 0.4

          // 如果元素主要部分在视口中且得分更高，则选择它
          if (totalScore > bestScore && entry.intersectionRatio > 0.3) {
            bestScore = totalScore
            bestEntry = entry
          }
        })

        const categoryUid = bestEntry.target.id.replace('category-section-', '')

        // 添加防抖逻辑，避免频繁切换
        if (categoryUid && categoryUid !== selectedCategoryUid) {
          // 清除之前的切换定时器
          if (switchTimeoutRef.current) {
            clearTimeout(switchTimeoutRef.current)
          }

          // 延迟切换，避免快速滚动时的频繁切换
          switchTimeoutRef.current = setTimeout(() => {
            reportEvent(TRACK_EVENT.shop_homepage_tab_click, {
              category_id: categoryUid,
              category_name: getCategoryDisplayName(categoryUid),
              click_position: 'scroll_auto',
              previous_category: selectedCategoryUid,
              trigger_type: 'scroll',
            })
            setSelectedCategoryUid(categoryUid)
          }, 150) // 150ms防抖延迟
        }
      },
      {
        root: contentElement,
        rootMargin: '-20% 0px -20% 0px', // 缩小触发区域到视口中心60%
        threshold: [0.1, 0.3, 0.5, 0.7], // 减少阈值数量，提高性能
      },
    )

    // 添加防抖的滚动事件监听
    contentElement.addEventListener('scroll', handleScroll)

    // 延迟观察，确保DOM元素已经渲染
    const timeoutId = setTimeout(() => {
      categorySections.forEach((section) => {
        const element = document.getElementById(`category-section-${section.uid}`)
        if (element) {
          observer.observe(element)
        }
      })
    }, 100)

    return () => {
      clearTimeout(timeoutId)
      if (scrollTimeout) clearTimeout(scrollTimeout)
      if (switchTimeoutRef.current) clearTimeout(switchTimeoutRef.current)
      observer.disconnect()
      contentElement.removeEventListener('scroll', handleScroll)
    }
  }, [
    isOpen,
    isScrolling,
    categorySections,
    selectedCategoryUid,
    getCategoryDisplayName,
    reportEvent,
  ])

  // 左侧导航栏动态定位：监听选中分类变化
  useEffect(() => {
    // 如果用户正在手动滚动，则不执行自动定位
    if (
      !leftNavNeedsScroll ||
      !selectedCategoryUid ||
      isScrolling ||
      isAutoScrolling ||
      isUserScrolling
    )
      return

    // 使用防抖避免频繁滚动
    const timeoutId = setTimeout(() => {
      scrollToSelectedCategory(selectedCategoryUid)
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [
    selectedCategoryUid,
    leftNavNeedsScroll,
    isScrolling,
    isAutoScrolling,
    isUserScrolling,
    scrollToSelectedCategory,
  ])

  // 处理弹窗打开和埋点，以及防止滚动穿透
  useEffect(() => {
    if (isOpen) {
      // 重置状态
      setSelectedCategoryUid('')
      setCategorySections([])
      setIsUserScrolling(false) // 重置用户滚动状态

      // 防止滚动穿透 - 保存当前滚动位置并锁定背景滚动
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

      // 添加样式以防止背景滚动
      document.body.style.position = 'fixed'
      document.body.style.top = `-${scrollTop}px`
      document.body.style.left = `-${scrollLeft}px`
      document.body.style.width = '100%'

      // 埋点：购车弹窗展示
      reportEvent(TRACK_EVENT.shop_classfication_exposure, {
        category_type: 'shopping_dropdown',
        category_name: '购车',
        exposure_time: Date.now(),
      })
    } else {
      // 恢复背景滚动
      const scrollTop = -parseInt(document.body.style.top || '0')
      const scrollLeft = -parseInt(document.body.style.left || '0')

      // 移除样式以恢复背景滚动
      document.body.style.position = ''
      document.body.style.top = ''
      document.body.style.left = ''
      document.body.style.width = ''

      // 恢复滚动位置
      window.scrollTo(scrollLeft, scrollTop)
    }
  }, [isOpen, reportEvent])

  if (!isOpen) return null

  return (
    <div
      ref={dropdownRef}
      data-dropdown
      data-shopping-dropdown
      className={clsx(
        'fixed left-0 right-0 top-[56px] z-50 w-screen bg-white shadow-2xl',
        isOpen ? 'opacity-100' : 'opacity-0',
      )}
      style={{
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={() => {
        onMouseLeave?.()
        handleClose()
      }}>
      <div className="max-container-no-mb w-full">
        <div className="mt-base-32 flex h-full flex-col md:flex-row">
          {/* 左侧分类导航 */}
          <div className="border-b border-[#00000026] pr-[48px] md:border-b-0 md:border-r 3xl:pr-[80px]">
            <div
              ref={leftNavRef}
              className={clsx(
                'scroll-hide space-y-base-32 overflow-y-auto scroll-smooth pb-40',
                isTouchDevice && 'mobile-scroll-optimized scroll-container',
              )}
              style={{
                maxHeight: calculateDropdownHeight(),
                touchAction: isTouchDevice ? 'pan-y' : 'auto',
                overscrollBehavior: isTouchDevice ? 'contain' : 'auto',
              }}>
              {categories?.map((category) => {
                if (!category) return null

                return (
                  <div key={category.uid}>
                    {/* L1级分类标题 */}
                    <div className="mb-base-12 font-miSansMedium380 text-[14px] leading-[140%] text-[#86868B]">
                      {category.name}
                    </div>

                    {/* L2级子分类 */}
                    <div className="space-y-base">
                      {category.children?.map((subCategory) => {
                        if (!subCategory || !subCategory.include_in_menu) return null

                        const isSelected = subCategory.uid === selectedCategoryUid

                        return (
                          <button
                            key={subCategory.uid}
                            data-category-uid={subCategory.uid}
                            className={clsx(
                              'relative block w-full text-left font-miSansMedium380 text-[18px] leading-[22px] transition-all duration-300 hover:text-primary 3xl:text-[20px]',
                              isSelected ? 'text-primary' : 'text-[#0F0F0F]',
                            )}
                            onClick={() =>
                              handleCategoryClick(subCategory.uid, subCategory.name || '')
                            }>
                            <span>{subCategory.name}</span>
                          </button>
                        )
                      })}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* 右侧产品展示区域 */}
          <div className="flex-1 pl-[48px] 3xl:pl-[80px]">
            <div
              ref={contentRef}
              className={clsx(
                'h-full overflow-y-auto scroll-smooth',
                isTouchDevice && 'mobile-scroll-optimized scroll-container',
              )}
              style={{
                maxHeight: calculateDropdownHeight(),
                touchAction: isTouchDevice ? 'pan-y' : 'auto',
                overscrollBehavior: isTouchDevice ? 'contain' : 'auto',
              }}>
              {isProductsLoading || Object.values(l2ProductsLoading).some((loading) => loading) ? (
                <div className="space-y-base-32">
                  {/* 加载骨架屏 */}
                  {Array.from({ length: 2 }).map((_, sectionIndex) => (
                    <div key={`skeleton-section-${sectionIndex}`}>
                      {/* 分类标题骨架 */}
                      <div className="mb-6 flex items-center gap-8">
                        <div className="h-6 w-32 animate-pulse rounded bg-gray-200" />
                        <div className="h-4 w-16 animate-pulse rounded bg-gray-200" />
                      </div>

                      {/* 产品网格骨架 */}
                      <div className="grid gap-base-12 md:grid-cols-4 md:gap-base-16">
                        {Array.from({ length: 4 }).map((_, productIndex) => (
                          <div key={`skeleton-product-${productIndex}`} className="aspect-square">
                            <div className="h-full w-full animate-pulse rounded-lg bg-gray-200" />
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : categorySections.length === 0 ? null : (
                <div className="space-y-base-48">
                  {categorySections.map((section, index) => {
                    const isCurrentSection = section.uid === selectedCategoryUid
                    const isLastSection = index === categorySections.length - 1

                    return (
                      <div
                        key={section.uid}
                        id={`category-section-${section.uid}`}
                        className={clsx(isLastSection && 'pb-[24px]')}>
                        {/* 分类标题和查看全部 */}
                        <div className="mb-8 flex items-center gap-8">
                          <div
                            className={clsx(
                              'font-miSansMedium380 text-[18px] leading-[120%] transition-colors duration-300 3xl:text-[20px]',
                              isCurrentSection ? 'text-primary' : 'text-[#0F0F0F]',
                            )}>
                            {section.name}
                          </div>
                          <button
                            className="flex items-center gap-1 text-[13px] text-[#444446] transition-all duration-200 hover:text-primary"
                            onClick={() => handleViewAllClick(section)}>
                            查看全部 <IconArrow color="#444446" rotate={-90} />
                          </button>
                        </div>

                        {/* 产品网格 */}
                        {section.products.length > 0 ? (
                          <div className="grid grid-cols-4">
                            {section.products.map((product) => {
                              // 检查产品数据完整性
                              const hasValidData = product && product.sku && product.name

                              return (
                                <div
                                  key={product.sku || `product-${Math.random()}`}
                                  className="group relative"
                                  onClick={() => hasValidData && handleProductClick(product)}>
                                  {hasValidData ? (
                                    <ProductCard
                                      product={product}
                                      currentTime={currentTime || Date.now().toString()}
                                      containerStyle={{
                                        height: '100%',
                                        backgroundColor: '#ffffff',
                                      }}
                                      enableImageHoverZoom={false}
                                      closeModal={() => handleProductClick(product)}
                                      imageContainerStyle={{
                                        borderRadius: '0px',
                                        backgroundColor: '#ffffff',
                                      }}
                                      centerInfoContent={true}
                                      showProductType={false}
                                      tagContainerStyle={{
                                        right: '12px',
                                        top: '12px',
                                      }}
                                      tagTextStyle={{
                                        fontFamily: 'MiSansDemiBold450',
                                      }}
                                      nameStyle="text-[18px] leading-[1.4] font-miSansMedium380"
                                      priceContainerStyle="mt-[6px]"
                                    />
                                  ) : (
                                    // 产品数据异常时的占位符
                                    <div className="flex h-full w-full items-center justify-center rounded-lg bg-gray-100">
                                      <span className="text-2xl text-[#6e6e73]">数据异常</span>
                                    </div>
                                  )}
                                </div>
                              )
                            })}
                          </div>
                        ) : null}
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
